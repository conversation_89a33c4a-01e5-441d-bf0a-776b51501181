import React, { create<PERSON>ontext, use<PERSON>ontext, use<PERSON>educer, ReactNode } from "react";
import { FileUploadState, FileUploadProgress } from "../types/upload";
import { ExportState } from "../types/export";
import { Gig } from "../types/gig";
import apiService from "../services/apiService";
import { guidedJourneyOrder } from "../data/SectionMetadata";

// Define the state structure
interface FileState {
  upload: FileUploadState;
  export: ExportState;
  showFileUpload: boolean;
  selectedFile: File | null;
}

// Define action types
type FileAction =
  | { type: "SET_UPLOAD_STATE"; payload: Partial<FileUploadState> }
  | { type: "SET_UPLOAD_PROGRESS"; payload: FileUploadProgress }
  | { type: "SET_UPLOAD_ERROR"; payload: string }
  | { type: "SET_UPLOAD_SUCCESS"; payload: boolean }
  | { type: "SET_UPLOAD_LOADING"; payload: boolean }
  | { type: "RESET_UPLOAD_STATE" }
  | { type: "SET_EXPORT_STATE"; payload: Partial<ExportState> }
  | { type: "SET_EXPORT_LOADING"; payload: boolean }
  | { type: "SET_EXPORT_ERROR"; payload: string }
  | {
      type: "SET_EXPORT_SUCCESS";
      payload: { success: boolean; format: string };
    }
  | { type: "RESET_EXPORT_STATE" }
  | { type: "SET_SHOW_FILE_UPLOAD"; payload: boolean }
  | { type: "SET_SELECTED_FILE"; payload: File | null }
  | {
      type: "UPLOAD_FILE";
      payload: { file: File; uploadType: "direct" | "guided" };
    }
  | { type: "RESET_ALL" };

// Initial state
const initialFileState: FileState = {
  upload: {
    isUploading: false,
    progress: null,
    error: null,
    success: false,
  },
  export: {
    isExporting: false,
    error: null,
    success: false,
    format: null,
  },
  showFileUpload: false,
  selectedFile: null,
};

// Reducer function
const fileReducer = (state: FileState, action: FileAction): FileState => {
  switch (action.type) {
    case "SET_UPLOAD_STATE":
      return {
        ...state,
        upload: {
          ...state.upload,
          ...action.payload,
        },
      };
    case "SET_UPLOAD_PROGRESS":
      return {
        ...state,
        upload: {
          ...state.upload,
          progress: action.payload,
        },
      };
    case "SET_UPLOAD_ERROR":
      return {
        ...state,
        upload: {
          ...state.upload,
          error: action.payload,
          isUploading: false,
        },
      };
    case "SET_UPLOAD_SUCCESS":
      return {
        ...state,
        upload: {
          ...state.upload,
          success: action.payload,
          isUploading: false,
          error: null,
        },
      };
    case "SET_UPLOAD_LOADING":
      return {
        ...state,
        upload: {
          ...state.upload,
          isUploading: action.payload,
          error: action.payload ? null : state.upload.error,
        },
      };
    case "RESET_UPLOAD_STATE":
      return {
        ...state,
        upload: initialFileState.upload,
      };
    case "SET_EXPORT_STATE":
      return {
        ...state,
        export: {
          ...state.export,
          ...action.payload,
        },
      };
    case "SET_EXPORT_LOADING":
      return {
        ...state,
        export: {
          ...state.export,
          isExporting: action.payload,
          error: action.payload ? null : state.export.error,
        },
      };
    case "SET_EXPORT_ERROR":
      return {
        ...state,
        export: {
          ...state.export,
          error: action.payload,
          isExporting: false,
        },
      };
    case "SET_EXPORT_SUCCESS":
      return {
        ...state,
        export: {
          ...state.export,
          success: action.payload.success,
          format: action.payload.format,
          isExporting: false,
          error: null,
        },
      };
    case "RESET_EXPORT_STATE":
      return {
        ...state,
        export: initialFileState.export,
      };
    case "SET_SHOW_FILE_UPLOAD":
      return {
        ...state,
        showFileUpload: action.payload,
      };
    case "SET_SELECTED_FILE":
      return {
        ...state,
        selectedFile: action.payload,
      };
    case "UPLOAD_FILE":
      // This case is handled by the side effect in useFileOperations
      return state;
    case "RESET_ALL":
      return initialFileState;
    default:
      return state;
  }
};

// Create context with types
interface FileContextType {
  state: FileState;
  dispatch: React.Dispatch<FileAction>;
}

const FileContext = createContext<FileContextType | undefined>(undefined);

// Provider component
export function FileProvider({ children }: { children: ReactNode }) {
  const [state, dispatch] = useReducer(fileReducer, initialFileState);

  return (
    <FileContext.Provider value={{ state, dispatch }}>
      {children}
    </FileContext.Provider>
  );
}

// Custom hook to use the context
export function useFile(): FileContextType {
  const context = useContext(FileContext);
  if (context === undefined) {
    throw new Error("useFile must be used within a FileProvider");
  }
  return context;
}

// Custom hook for file operations
export function useFileOperations() {
  const { state, dispatch } = useFile();

  const setUploadState = (uploadState: Partial<FileUploadState>) => {
    dispatch({ type: "SET_UPLOAD_STATE", payload: uploadState });
  };

  const setUploadProgress = (progress: FileUploadProgress) => {
    dispatch({ type: "SET_UPLOAD_PROGRESS", payload: progress });
  };

  const setUploadError = (error: string) => {
    dispatch({ type: "SET_UPLOAD_ERROR", payload: error });
  };

  const setUploadSuccess = (success: boolean) => {
    dispatch({ type: "SET_UPLOAD_SUCCESS", payload: success });
  };

  const setUploadLoading = (loading: boolean) => {
    dispatch({ type: "SET_UPLOAD_LOADING", payload: loading });
  };

  const resetUploadState = () => {
    dispatch({ type: "RESET_UPLOAD_STATE" });
  };

  const setExportState = (exportState: Partial<ExportState>) => {
    dispatch({ type: "SET_EXPORT_STATE", payload: exportState });
  };

  const setExportLoading = (loading: boolean) => {
    dispatch({ type: "SET_EXPORT_LOADING", payload: loading });
  };

  const setExportError = (error: string) => {
    dispatch({ type: "SET_EXPORT_ERROR", payload: error });
  };

  const setExportSuccess = (success: boolean, format: string) => {
    dispatch({ type: "SET_EXPORT_SUCCESS", payload: { success, format } });
  };

  const resetExportState = () => {
    dispatch({ type: "RESET_EXPORT_STATE" });
  };

  const setShowFileUpload = (show: boolean) => {
    dispatch({ type: "SET_SHOW_FILE_UPLOAD", payload: show });
  };

  const openFileUploadModal = () => {
    dispatch({ type: "SET_SHOW_FILE_UPLOAD", payload: true });
  };

  const closeFileUploadModal = () => {
    dispatch({ type: "SET_SHOW_FILE_UPLOAD", payload: false });
    resetUploadState();
  };

  const setSelectedFile = (file: File | null) => {
    dispatch({ type: "SET_SELECTED_FILE", payload: file });
  };

  const resetAll = () => {
    dispatch({ type: "RESET_ALL" });
  };

  const uploadFile = async (
    file: File,
    uploadType: "direct" | "guided",
    navigate: (path: string) => void,
    setGig: (gig: Gig) => void,
    setActiveSection?: (section: keyof Gig | null) => void,
    generateSuggestionWithGig?: (section: keyof Gig, gig: Gig) => void
  ) => {
    setUploadLoading(true);
    setUploadProgress({ loaded: 0, total: file.size, percentage: 0 });

    try {
      const response = await apiService.uploadAndConvertFile(
        file,
        (percentage) => {
          setUploadProgress({
            loaded: (file.size * percentage) / 100,
            total: file.size,
            percentage,
          });
        }
      );

      if (response.success && response.gig) {
        setUploadSuccess(true);
        setGig(response.gig);

        if (uploadType === "direct") {
          navigate("/content");
        } else if (
          uploadType === "guided" &&
          setActiveSection &&
          generateSuggestionWithGig
        ) {
          const firstSection = guidedJourneyOrder[0];
          setActiveSection(firstSection);
          navigate(`/guided-journey/${firstSection}`);
          generateSuggestionWithGig(firstSection, response.gig);
        }
      } else {
        throw new Error(response.message || "Upload failed");
      }
    } catch (error) {
      setUploadError(error instanceof Error ? error.message : "Upload failed");
    }
  };

  return {
    // State
    uploadState: state.upload,
    exportState: state.export,
    showFileUpload: state.showFileUpload,
    selectedFile: state.selectedFile,

    // Upload operations
    setUploadState,
    setUploadProgress,
    setUploadError,
    setUploadSuccess,
    setUploadLoading,
    resetUploadState,
    uploadFile,

    // Export operations
    setExportState,
    setExportLoading,
    setExportError,
    setExportSuccess,
    resetExportState,

    // UI operations
    setShowFileUpload,
    openFileUploadModal,
    closeFileUploadModal,
    setSelectedFile,
    resetAll,
  };
}
